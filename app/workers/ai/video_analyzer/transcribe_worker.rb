# frozen_string_literal: true

module Ai
  module VideoAnalyzer
    class TranscribeWorker < ApplicationWorker
      # TODO: Changes to use offset on gemini
      def perform(transcribe_id, main_worker_id)
        transcribe_run = Ai::AssistantRun.find(transcribe_id)
        return unless transcribe_run.status.in?(%w[queued in_progress])

        @main_worker = Ai::AssistantRun.find(main_worker_id)
        @main_worker.transcribing!

        video_type = transcribe_run.assistant.purpose.gsub('_transcriber', '')
        video_processor_run = Ai::AssistantRun.joins(:assistant)
                                              .find_by(
                                                thread_id: transcribe_run.thread_id,
                                                ai_assistants: { purpose: "#{video_type}_video_processor" }
                                              )

        raise 'Video processor run not found' unless video_processor_run.present?
        unless video_processor_run.status == 'completed'
          raise 'Video processing still not completed'
        end

        transcribe_run.in_progress!

        chunk_transcriber_assistant = Ai::Assistant.find_by(purpose: "#{video_type}_chunk_transcriber")

        file_url = video_processor_run.file_url
        chunks_records = []
        # chunked_urls is an array of hashes
        video_processor_run.json_response['chunks'].each do |chunk|
          where_sql = <<~SQL
            (request_raw->>'start_time')::float = ? AND
            (request_raw->>'end_time')::float = ?
          SQL

          params_where = {
            thread_id: transcribe_run.thread_id,
            ai_assistant_id: chunk_transcriber_assistant.id,
            user_id: transcribe_run.user_id
          }

          chunk_run = Ai::AssistantRun
                      .where(where_sql, chunk['start_time'], chunk['end_time'])
                      .where(params_where)
                      .first

          chunk_run ||= Ai::AssistantRun.create!(
            params_where.merge(
              status: 'queued',
              file_url: file_url,
              request_raw: {
                start_time: chunk['start_time'],
                end_time: chunk['end_time']
              }
            )
          )

          chunks_records << {
            assistant_run_chunk_id: chunk_run.id,
            status: chunk_run.status,
            starts_at: chunk_run.completed? ? chunk_run.created_at : nil,
            completed_at: chunk_run.completed? ? chunk_run.updated_at : nil
          }
        end

        raise 'No chunks to transcribe' if chunks_records.empty?

        transcribe_run.update!(request_raw: { chunks: chunks_records })

        chunk_ids = chunks_records.map { |r| r[:assistant_run_chunk_id] }

        chunk_runs = Ai::AssistantRun.where(id: chunk_ids)
                                     .select(
                                       :status, :id, :created_at,
                                       :updated_at
                                     )

        transcribe_completed = chunk_runs.all? { |r| r.status == 'completed' }

        if transcribe_completed
          joined_trans = join_transcripts(chunk_ids)
          compile_transcript(transcribe_run, joined_trans)
          return
        end

        chunk_runs.each do |chunk_run|
          chunk_record = chunks_records.find { |r| r[:assistant_run_chunk_id] == chunk_run.id }
          chunk_record[:status] = chunk_run.status
          chunk_record[:starts_at] = chunk_run.created_at if chunk_run.status == 'in_progress'
          chunk_record[:completed_at] = chunk_run.updated_at if chunk_run.status == 'completed'
        end

        in_progress_count = chunks_records.count { |r| r[:status] == 'in_progress' }
        queued_count = chunks_records.count { |r| r[:status] == 'queued' }
        failed_count = chunks_records.count { |r| r[:status] == 'failed' }

        raise 'Chunk Transcription failed' if failed_count.positive?

        limit_progressed = (org_internal_config.dig(
          'video_analyzer',
          'transcribe_chunk_limit'
        ) || 5).to_i

        if in_progress_count < limit_progressed && queued_count.positive?
          size_to_run = limit_progressed - in_progress_count

          chunks_records.select { |r| r[:status] == 'queued' }
                        .first(size_to_run)
                        .each do |chunk_record|
            chunk_record[:status] = 'in_progress'
            chunk_record[:starts_at] = Time.current

            chunk_id = chunk_record[:assistant_run_chunk_id]
            Ai::VideoAnalyzer::TranscribeChunkWorker.perform_async(chunk_id, transcribe_id,
                                                                   main_worker_id)
            current_run = Ai::AssistantRun.find(chunk_id)
            current_run.in_progress!
          end
        end

        transcribe_run.update!(request_raw: { chunks: chunks_records })

        transcribe_completed = chunks_records.all? { |r| r[:status] == 'completed' }

        unless transcribe_completed
          Ai::VideoAnalyzer::TranscribeWorker.perform_at(
            15.seconds.from_now,
            transcribe_id,
            main_worker_id
          )

          return
        end

        transcribe_run.update!(request_raw: { chunks: chunks_records })

        transcript = join_transcripts(chunk_ids)
        compile_transcript(transcribe_run, transcript)

        transcribe_run.completed!
      rescue StandardError => e
        Sentry.capture_exception(e)

        transcribe_run.update!(
          status: 'failed',
          raw_response: e
        )
      ensure
        purge_file(video_processor_run)
      end

      private

      def purge_file(file_url_container)
        return if file_url_container&.file_url.blank?

        file_url = file_url_container.file_url
        file_name = file_url.split('/').last
        @google_ai_service.get_file(file_name:)
        @google_ai_service.delete_file(file_name: file_name)
      rescue StandardError
        nil
      end

      def join_transcripts(chunk_ids)
        chunks = Ai::AssistantRun.where(id: chunk_ids).select(:json_response, :id, :request_raw)

        transcripts = <<~TRANSCRIPT
          Here is the chunked transcript
        TRANSCRIPT

        sorted_chunks = chunks.sort_by { |chunk| chunk.request_raw['start_time'] }
        sorted_chunks.each do |chunk|
          added_string = <<~STRING
            Transcript:
            #{chunk.json_response['transcript']}
            \n\n
          STRING

          transcripts += added_string
        end

        transcripts
      end

      def compile_transcript(run, transcripts)
        assistant = run.assistant

        list_participant_prompts = <<~PROMPT
          Here is the participant lists
          Name: Interviewer/Moderator
          ID: 0
        PROMPT

        @main_worker.request_raw['users'].each do |user|
          list_participant_prompts += <<~PROMPT
            ID: #{user['id']}
            Name: #{user['name']}
            \n\n
          PROMPT
        end

        user_prompt = <<~PROMPT
          Please compile the following transcript into a single transcript

          #{transcripts}

          #{list_participant_prompts}
        PROMPT

        ai_params = {
          model: assistant.model,
          system_instruction: {
            parts: [
              {
                text: assistant.system_instruction
              }
            ]
          },
          contents: [
            {
              role: 'user',
              parts: [
                {
                  text: user_prompt
                }
              ]
            }
          ],
          generationConfig: {
            temperature: assistant.temperature,
            responseMimeType: assistant.response_mime_type
          }
        }

        if assistant.thinking_budget.present?
          ai_params[:generationConfig][:thinkingConfig] = {
            thinkingBudget: assistant.thinking_budget
          }
        end

        response = google_ai_service.generate_content(ai_params)
        raw_responses = response
        text_response = response.dig('candidates', 0, 'content', 'parts', 0, 'text')
        total_token = response.dig('usageMetadata', 'totalTokenCount')

        parsed_response = safe_parse_json(text_response)

        run.status = 'completed'
        run.json_response = { compiled_transcript: parsed_response }
        run.raw_response = JSON.dump(raw_responses)
        run.total_token = total_token

        current_chunks = run.request_raw['chunks']
        run.request_raw = {
          chunks: current_chunks,
          compiled_transcript: ai_params
        }

        run.save!
      end

      def google_ai_service
        @google_ai_service ||= ::External::GoogleAiService.new
      end
    end
  end
end
