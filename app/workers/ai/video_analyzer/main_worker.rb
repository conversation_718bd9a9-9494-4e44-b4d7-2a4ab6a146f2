# frozen_string_literal: true

module Ai
  module VideoAnalyzer
    class MainWorker < ApplicationWorker
      def perform(run_id)
        run = Ai::AssistantRun.find(run_id)
        return if run.status.in?(%w[completed failed])

        assistant = run.assistant
        unless video_analyzer_prompts.include?(assistant.purpose)
          run.update!(status: 'failed', raw_response: 'Invalid assistant purpose')
          return
        end

        video_type = assistant.purpose.gsub('_assistant', '')
        in_progress_main = Ai::AssistantRun.where(
          ai_assistant_id: assistant.id,
          status: %w[in_progress video_processing transcribing analyzing]
        ).where.not(id: run.id).first

        return if in_progress_main.present?

        assistants = find_required_assistants(video_type)
        unless assistants_present?(assistants)
          run.update!(status: 'failed', raw_response: 'Required assistants not found')
          return
        end

        status = {
          video_processing_status: 'queued',
          transcription_status: 'queued',
          analysis_status: 'queued'
        }

        assistant_ids = assistants.values.map(&:id)
        thread_id = run.thread_id
        select_columns = 'ai_assistants.purpose AS assistant_purpose, ai_assistant_runs.*'
        main_runners = Ai::AssistantRun.joins(:assistant)
                                       .where(thread_id:, ai_assistant_id: assistant_ids)
                                       .select(select_columns)

        has_in_progress = main_runners.any? { |r| r.status == 'in_progress' }
        all_completed = main_runners.all? { |r| r.status == 'completed' }

        run.completed! if all_completed

        mapped_main_workers(video_type).each do |status_key, worker_purpose|
          current_runner = main_runners.find { |r| r.assistant_purpose == worker_purpose }

          current_runner ||= seed_runner(assistants[worker_purpose], thread_id, run.user_id)
          status[status_key] = current_runner.status

          if status[status_key] == 'in_progress'
            has_in_progress = true
            ::Ai::VideoAnalyzer::MainWorker.perform_at(15.seconds.from_now, run.id)
            break
          end

          if status[status_key] == 'failed'
            run.failed!
            break
          end

          next if status[status_key] == 'completed'
          next unless status[status_key] == 'queued' && !has_in_progress

          ActiveRecord::Base.transaction do
            status[status_key] = 'in_progress'
            current_runner.in_progress!
            worker_runner(current_runner.id, run.id, worker_purpose)
          end

          ::Ai::VideoAnalyzer::MainWorker.perform_at(15.seconds.from_now, run.id)

          has_in_progress = true
          break
        end
      rescue StandardError => e
        Sentry.capture_exception(e)
        run.update!(status: 'failed', raw_response: e)
      end

      private

      def worker_runner(run_id, main_worker_id, purpose)
        if purpose.include?('transcriber')
          Ai::VideoAnalyzer::TranscribeWorker.perform_async(run_id, main_worker_id)
        end

        if purpose.include?('video_processor')
          Ai::VideoAnalyzer::VideoProcessingWorker.perform_async(run_id, main_worker_id)
        end

        return unless purpose.include?('analyzer')

        Ai::VideoAnalyzer::AnalyzeWorker.perform_async(run_id, main_worker_id)
      end

      def find_required_assistants(video_type)
        {
          transcriber: Ai::Assistant.find_by(purpose: "#{video_type}_transcriber"),
          analyzer: Ai::Assistant.find_by(purpose: "#{video_type}_analyzer"),
          video_processor: Ai::Assistant.find_by(purpose: "#{video_type}_video_processor")
        }
      end

      def assistants_present?(assistants)
        assistants.values.all?(&:present?)
      end

      def seed_runner(assistant, thread_id, user_id)
        Ai::AssistantRun.create!(
          thread_id:,
          ai_assistant_id: assistant.id,
          status: 'queued',
          user_id:
        )
      end

      def mapped_main_workers(video_type)
        {
          video_processing_status: "#{video_type}_video_processor",
          transcription_status: "#{video_type}_transcriber",
          analysis_status: "#{video_type}_analyzer"
        }
      end

      def video_analyzer_prompts
        org_internal_config['video_analyzer_prompts'] || []
      end
    end
  end
end
